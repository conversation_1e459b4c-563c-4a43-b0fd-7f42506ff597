# Environment variables
.env

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
.venv/
venv/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Logs
*.log

# Jupyter Notebook
.ipynb_checkpoints

# Local development
.DS_Store

# Distribution
*.log
logs/
*.pot
*.pyc
local_settings.py
db.sqlite3
db.sqlite3-journal

# Distribution
*.bak
*.tmp
*.temp